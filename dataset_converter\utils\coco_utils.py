"""
COCO format utilities for dataset conversion
"""
import json
import os
from datetime import datetime
from typing import Dict, List, Optional, Union, Tuple
from pathlib import Path
import numpy as np
import cv2
from pycocotools import mask as coco_mask
from .common import load_json, save_json, get_image_size


class COCODataset:
    """COCO dataset handler"""
    
    def __init__(self, annotation_file: Optional[str] = None):
        self.info = {
            "description": "Converted dataset",
            "version": "1.0",
            "year": datetime.now().year,
            "contributor": "Dataset Converter",
            "date_created": datetime.now().isoformat()
        }
        self.licenses = [{"id": 1, "name": "Unknown", "url": ""}]
        self.categories = []
        self.images = []
        self.annotations = []
        
        if annotation_file and os.path.exists(annotation_file):
            self.load_annotations(annotation_file)
    
    def load_annotations(self, annotation_file: str) -> None:
        """Load COCO annotations from file"""
        data = load_json(annotation_file)
        self.info = data.get('info', self.info)
        self.licenses = data.get('licenses', self.licenses)
        self.categories = data.get('categories', [])
        self.images = data.get('images', [])
        self.annotations = data.get('annotations', [])
    
    def save_annotations(self, output_file: str) -> None:
        """Save COCO annotations to file"""
        coco_data = {
            "info": self.info,
            "licenses": self.licenses,
            "categories": self.categories,
            "images": self.images,
            "annotations": self.annotations
        }
        save_json(coco_data, output_file)
    
    def add_category(self, category_id: int, name: str, supercategory: str = "") -> None:
        """Add category to dataset"""
        category = {
            "id": category_id,
            "name": name,
            "supercategory": supercategory
        }
        self.categories.append(category)
    
    def add_image(self, image_id: int, file_name: str, width: int, height: int) -> None:
        """Add image to dataset"""
        image = {
            "id": image_id,
            "file_name": file_name,
            "width": width,
            "height": height
        }
        self.images.append(image)
    
    def add_detection_annotation(self, annotation_id: int, image_id: int, 
                                category_id: int, bbox: List[float], 
                                area: Optional[float] = None, iscrowd: int = 0) -> None:
        """Add object detection annotation"""
        if area is None:
            area = bbox[2] * bbox[3]  # width * height
        
        annotation = {
            "id": annotation_id,
            "image_id": image_id,
            "category_id": category_id,
            "bbox": bbox,  # [x, y, width, height]
            "area": area,
            "iscrowd": iscrowd
        }
        self.annotations.append(annotation)
    
    def add_segmentation_annotation(self, annotation_id: int, image_id: int,
                                   category_id: int, bbox: List[float],
                                   segmentation: Union[List[List[float]], Dict],
                                   area: Optional[float] = None, iscrowd: int = 0) -> None:
        """Add instance segmentation annotation"""
        if area is None:
            if isinstance(segmentation, list):
                # Polygon format
                area = self._polygon_area(segmentation[0])
            else:
                # RLE format
                area = coco_mask.area(segmentation)
        
        annotation = {
            "id": annotation_id,
            "image_id": image_id,
            "category_id": category_id,
            "bbox": bbox,
            "segmentation": segmentation,
            "area": area,
            "iscrowd": iscrowd
        }
        self.annotations.append(annotation)
    
    def add_keypoint_annotation(self, annotation_id: int, image_id: int,
                               category_id: int, bbox: List[float],
                               keypoints: List[float], num_keypoints: int,
                               area: Optional[float] = None, iscrowd: int = 0) -> None:
        """Add keypoint detection annotation"""
        if area is None:
            area = bbox[2] * bbox[3]
        
        annotation = {
            "id": annotation_id,
            "image_id": image_id,
            "category_id": category_id,
            "bbox": bbox,
            "keypoints": keypoints,  # [x1, y1, v1, x2, y2, v2, ...]
            "num_keypoints": num_keypoints,
            "area": area,
            "iscrowd": iscrowd
        }
        self.annotations.append(annotation)
    
    def add_keypoint_category(self, category_id: int, name: str, 
                             keypoints: List[str], skeleton: List[List[int]]) -> None:
        """Add keypoint category with skeleton definition"""
        category = {
            "id": category_id,
            "name": name,
            "supercategory": "person",
            "keypoints": keypoints,
            "skeleton": skeleton
        }
        self.categories.append(category)
    
    def get_category_mapping(self) -> Dict[str, int]:
        """Get category name to ID mapping"""
        return {cat['name']: cat['id'] for cat in self.categories}
    
    def get_image_by_filename(self, filename: str) -> Optional[Dict]:
        """Get image info by filename"""
        for img in self.images:
            if img['file_name'] == filename:
                return img
        return None
    
    def get_annotations_by_image_id(self, image_id: int) -> List[Dict]:
        """Get all annotations for a specific image"""
        return [ann for ann in self.annotations if ann['image_id'] == image_id]
    
    @staticmethod
    def _polygon_area(polygon: List[float]) -> float:
        """Calculate polygon area using shoelace formula"""
        x = polygon[0::2]
        y = polygon[1::2]
        return 0.5 * abs(sum(x[i] * y[i + 1] - x[i + 1] * y[i] 
                            for i in range(-1, len(x) - 1)))


def create_coco_keypoint_categories() -> List[Dict]:
    """Create standard COCO keypoint categories"""
    # COCO person keypoints
    keypoints = [
        "nose", "left_eye", "right_eye", "left_ear", "right_ear",
        "left_shoulder", "right_shoulder", "left_elbow", "right_elbow",
        "left_wrist", "right_wrist", "left_hip", "right_hip",
        "left_knee", "right_knee", "left_ankle", "right_ankle"
    ]
    
    # COCO skeleton connections
    skeleton = [
        [16, 14], [14, 12], [17, 15], [15, 13], [12, 13],
        [6, 12], [7, 13], [6, 7], [6, 8], [7, 9],
        [8, 10], [9, 11], [2, 3], [1, 2], [1, 3],
        [2, 4], [3, 5], [4, 6], [5, 7]
    ]
    
    return [{
        "id": 1,
        "name": "person",
        "supercategory": "person",
        "keypoints": keypoints,
        "skeleton": skeleton
    }]


def polygon_to_rle(polygon: List[float], height: int, width: int) -> Dict:
    """Convert polygon to RLE format"""
    # Convert polygon to binary mask
    mask = np.zeros((height, width), dtype=np.uint8)
    polygon_array = np.array(polygon).reshape(-1, 2).astype(np.int32)
    cv2.fillPoly(mask, [polygon_array], 1)
    
    # Convert to RLE
    rle = coco_mask.encode(np.asfortranarray(mask))
    rle['counts'] = rle['counts'].decode('utf-8')
    return rle


def rle_to_polygon(rle: Dict) -> List[List[float]]:
    """Convert RLE to polygon format (approximate)"""
    # Decode RLE to binary mask
    mask = coco_mask.decode(rle)
    
    # Find contours
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    polygons = []
    for contour in contours:
        if len(contour) >= 3:  # Valid polygon needs at least 3 points
            polygon = contour.flatten().tolist()
            polygons.append(polygon)
    
    return polygons
