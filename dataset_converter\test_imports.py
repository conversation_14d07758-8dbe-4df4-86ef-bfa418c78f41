#!/usr/bin/env python3
"""
测试脚本，验证所有模块是否可以正确导入
"""
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入所有模块"""
    print("测试模块导入...")
    print("=" * 40)

    # 测试工具模块
    try:
        from utils import common
        print("✓ utils.common")
    except ImportError as e:
        print(f"✗ utils.common: {e}")

    try:
        from utils import coco_utils
        print("✓ utils.coco_utils")
    except ImportError as e:
        print(f"✗ utils.coco_utils: {e}")

    try:
        from utils import yolo_utils
        print("✓ utils.yolo_utils")
    except ImportError as e:
        print(f"✗ utils.yolo_utils: {e}")

    try:
        from utils import voc_utils
        print("✓ utils.voc_utils")
    except ImportError as e:
        print(f"✗ utils.voc_utils: {e}")

    try:
        from utils import labelme_utils
        print("✓ utils.labelme_utils")
    except ImportError as e:
        print(f"✗ utils.labelme_utils: {e}")

    print()
    
    # Test converter modules
    try:
        from converters import coco_to_yolo
        print("✓ converters.coco_to_yolo")
    except ImportError as e:
        print(f"✗ converters.coco_to_yolo: {e}")
    
    try:
        from converters import yolo_to_coco
        print("✓ converters.yolo_to_coco")
    except ImportError as e:
        print(f"✗ converters.yolo_to_coco: {e}")
    
    try:
        from converters import voc_to_coco
        print("✓ converters.voc_to_coco")
    except ImportError as e:
        print(f"✗ converters.voc_to_coco: {e}")
    
    try:
        from converters import coco_to_voc
        print("✓ converters.coco_to_voc")
    except ImportError as e:
        print(f"✗ converters.coco_to_voc: {e}")
    
    try:
        from converters import yolo_to_voc
        print("✓ converters.yolo_to_voc")
    except ImportError as e:
        print(f"✗ converters.yolo_to_voc: {e}")
    
    try:
        from converters import voc_to_yolo
        print("✓ converters.voc_to_yolo")
    except ImportError as e:
        print(f"✗ converters.voc_to_yolo: {e}")
    
    try:
        from converters import imagenet_to_yolo_cls
        print("✓ converters.imagenet_to_yolo_cls")
    except ImportError as e:
        print(f"✗ converters.imagenet_to_yolo_cls: {e}")
    
    try:
        from converters import yolo_cls_to_imagenet
        print("✓ converters.yolo_cls_to_imagenet")
    except ImportError as e:
        print(f"✗ converters.yolo_cls_to_imagenet: {e}")

    try:
        from converters import labelme_to_coco
        print("✓ converters.labelme_to_coco")
    except ImportError as e:
        print(f"✗ converters.labelme_to_coco: {e}")

    try:
        from converters import labelme_to_yolo
        print("✓ converters.labelme_to_yolo")
    except ImportError as e:
        print(f"✗ converters.labelme_to_yolo: {e}")

    try:
        from converters import coco_to_labelme
        print("✓ converters.coco_to_labelme")
    except ImportError as e:
        print(f"✗ converters.coco_to_labelme: {e}")

    try:
        from converters import yolo_to_labelme
        print("✓ converters.yolo_to_labelme")
    except ImportError as e:
        print(f"✗ converters.yolo_to_labelme: {e}")

    print()
    print("导入测试完成！")


def test_basic_functionality():
    """Test basic functionality of key classes"""
    print("\nTesting basic functionality...")
    print("=" * 40)
    
    try:
        from utils.coco_utils import COCODataset
        coco_dataset = COCODataset()
        coco_dataset.add_category(1, "test_class")
        print("✓ COCODataset basic functionality")
    except Exception as e:
        print(f"✗ COCODataset: {e}")
    
    try:
        from utils.yolo_utils import YOLODataset
        yolo_dataset = YOLODataset()
        yolo_dataset.add_class("test_class")
        print("✓ YOLODataset basic functionality")
    except Exception as e:
        print(f"✗ YOLODataset: {e}")
    
    try:
        from utils.voc_utils import VOCDataset
        voc_dataset = VOCDataset()
        print("✓ VOCDataset basic functionality")
    except Exception as e:
        print(f"✗ VOCDataset: {e}")
    
    try:
        from utils.common import ensure_dir, get_image_size
        print("✓ Common utilities")
    except Exception as e:
        print(f"✗ Common utilities: {e}")
    
    print("\nBasic functionality test completed!")


def check_dependencies():
    """Check if all required dependencies are available"""
    print("\nChecking dependencies...")
    print("=" * 40)
    
    dependencies = [
        'numpy',
        'PIL',  # Pillow
        'cv2',  # opencv-python
        'tqdm',
        'yaml',  # PyYAML
        'lxml',
        'matplotlib',
        'skimage',  # scikit-image
    ]
    
    for dep in dependencies:
        try:
            if dep == 'PIL':
                import PIL
            elif dep == 'cv2':
                import cv2
            elif dep == 'yaml':
                import yaml
            elif dep == 'skimage':
                import skimage
            else:
                __import__(dep)
            print(f"✓ {dep}")
        except ImportError:
            print(f"✗ {dep} (not installed)")
    
    # Special check for pycocotools
    try:
        from pycocotools import mask
        print("✓ pycocotools")
    except ImportError:
        print("✗ pycocotools (not installed)")
    
    print("\nDependency check completed!")


def check_conda_environment():
    """检查conda环境"""
    print("\n检查conda环境...")
    print("=" * 40)

    import os
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env:
        print(f"✓ 当前conda环境: {conda_env}")
    else:
        print("✗ 未检测到conda环境")

    # 检查conda版本
    try:
        import subprocess
        result = subprocess.run(['conda', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ Conda版本: {result.stdout.strip()}")
        else:
            print("✗ 无法获取conda版本")
    except:
        print("✗ conda命令不可用")


def main():
    """主测试函数"""
    print("数据集转换器 - 模块测试")
    print("=" * 50)

    # 检查conda环境
    check_conda_environment()

    # 测试导入
    test_imports()

    # 测试基本功能
    test_basic_functionality()

    # 检查依赖
    check_dependencies()

    print("\n" + "=" * 50)
    print("测试完成!")
    print("\n如果您看到任何✗标记，您可能需要:")
    print("1. 使用conda安装依赖: conda install -c conda-forge numpy pillow opencv matplotlib scikit-image lxml tqdm pyyaml")
    print("2. 使用pip安装特殊依赖: pip install pycocotools")
    print("3. 或运行自动设置脚本: python conda_setup.py")
    print("4. 检查您的Python路径和模块结构")
    print("5. 确保所有文件都在正确的位置")


if __name__ == "__main__":
    main()
