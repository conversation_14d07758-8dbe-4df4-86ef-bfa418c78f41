#!/usr/bin/env python3
"""
Test script to verify all modules can be imported correctly
"""
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test importing all modules"""
    print("Testing module imports...")
    print("=" * 40)
    
    # Test utility modules
    try:
        from utils import common
        print("✓ utils.common")
    except ImportError as e:
        print(f"✗ utils.common: {e}")
    
    try:
        from utils import coco_utils
        print("✓ utils.coco_utils")
    except ImportError as e:
        print(f"✗ utils.coco_utils: {e}")
    
    try:
        from utils import yolo_utils
        print("✓ utils.yolo_utils")
    except ImportError as e:
        print(f"✗ utils.yolo_utils: {e}")
    
    try:
        from utils import voc_utils
        print("✓ utils.voc_utils")
    except ImportError as e:
        print(f"✗ utils.voc_utils: {e}")
    
    print()
    
    # Test converter modules
    try:
        from converters import coco_to_yolo
        print("✓ converters.coco_to_yolo")
    except ImportError as e:
        print(f"✗ converters.coco_to_yolo: {e}")
    
    try:
        from converters import yolo_to_coco
        print("✓ converters.yolo_to_coco")
    except ImportError as e:
        print(f"✗ converters.yolo_to_coco: {e}")
    
    try:
        from converters import voc_to_coco
        print("✓ converters.voc_to_coco")
    except ImportError as e:
        print(f"✗ converters.voc_to_coco: {e}")
    
    try:
        from converters import coco_to_voc
        print("✓ converters.coco_to_voc")
    except ImportError as e:
        print(f"✗ converters.coco_to_voc: {e}")
    
    try:
        from converters import yolo_to_voc
        print("✓ converters.yolo_to_voc")
    except ImportError as e:
        print(f"✗ converters.yolo_to_voc: {e}")
    
    try:
        from converters import voc_to_yolo
        print("✓ converters.voc_to_yolo")
    except ImportError as e:
        print(f"✗ converters.voc_to_yolo: {e}")
    
    try:
        from converters import imagenet_to_yolo_cls
        print("✓ converters.imagenet_to_yolo_cls")
    except ImportError as e:
        print(f"✗ converters.imagenet_to_yolo_cls: {e}")
    
    try:
        from converters import yolo_cls_to_imagenet
        print("✓ converters.yolo_cls_to_imagenet")
    except ImportError as e:
        print(f"✗ converters.yolo_cls_to_imagenet: {e}")
    
    print()
    print("Import test completed!")


def test_basic_functionality():
    """Test basic functionality of key classes"""
    print("\nTesting basic functionality...")
    print("=" * 40)
    
    try:
        from utils.coco_utils import COCODataset
        coco_dataset = COCODataset()
        coco_dataset.add_category(1, "test_class")
        print("✓ COCODataset basic functionality")
    except Exception as e:
        print(f"✗ COCODataset: {e}")
    
    try:
        from utils.yolo_utils import YOLODataset
        yolo_dataset = YOLODataset()
        yolo_dataset.add_class("test_class")
        print("✓ YOLODataset basic functionality")
    except Exception as e:
        print(f"✗ YOLODataset: {e}")
    
    try:
        from utils.voc_utils import VOCDataset
        voc_dataset = VOCDataset()
        print("✓ VOCDataset basic functionality")
    except Exception as e:
        print(f"✗ VOCDataset: {e}")
    
    try:
        from utils.common import ensure_dir, get_image_size
        print("✓ Common utilities")
    except Exception as e:
        print(f"✗ Common utilities: {e}")
    
    print("\nBasic functionality test completed!")


def check_dependencies():
    """Check if all required dependencies are available"""
    print("\nChecking dependencies...")
    print("=" * 40)
    
    dependencies = [
        'numpy',
        'PIL',  # Pillow
        'cv2',  # opencv-python
        'tqdm',
        'yaml',  # PyYAML
        'lxml',
        'matplotlib',
        'skimage',  # scikit-image
    ]
    
    for dep in dependencies:
        try:
            if dep == 'PIL':
                import PIL
            elif dep == 'cv2':
                import cv2
            elif dep == 'yaml':
                import yaml
            elif dep == 'skimage':
                import skimage
            else:
                __import__(dep)
            print(f"✓ {dep}")
        except ImportError:
            print(f"✗ {dep} (not installed)")
    
    # Special check for pycocotools
    try:
        from pycocotools import mask
        print("✓ pycocotools")
    except ImportError:
        print("✗ pycocotools (not installed)")
    
    print("\nDependency check completed!")


def main():
    """Main test function"""
    print("Dataset Converter - Module Test")
    print("=" * 50)
    
    # Test imports
    test_imports()
    
    # Test basic functionality
    test_basic_functionality()
    
    # Check dependencies
    check_dependencies()
    
    print("\n" + "=" * 50)
    print("Test completed!")
    print("\nIf you see any ✗ marks above, you may need to:")
    print("1. Install missing dependencies: pip install -r requirements.txt")
    print("2. Check your Python path and module structure")
    print("3. Ensure all files are in the correct locations")


if __name__ == "__main__":
    main()
