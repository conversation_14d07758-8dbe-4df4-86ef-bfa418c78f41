"""
Common utility functions for dataset conversion
"""
import os
import json
import shutil
from pathlib import Path
from typing import List, Dict, Tuple, Optional, Union
import cv2
import numpy as np
from PIL import Image


def ensure_dir(directory: Union[str, Path]) -> None:
    """Create directory if it doesn't exist"""
    Path(directory).mkdir(parents=True, exist_ok=True)


def get_image_size(image_path: Union[str, Path]) -> Tuple[int, int]:
    """Get image width and height"""
    try:
        with Image.open(image_path) as img:
            return img.size  # (width, height)
    except Exception as e:
        print(f"Error reading image {image_path}: {e}")
        return (0, 0)


def get_image_files(directory: Union[str, Path], extensions: List[str] = None) -> List[Path]:
    """Get all image files in directory"""
    if extensions is None:
        extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
    
    directory = Path(directory)
    image_files = []
    
    for ext in extensions:
        image_files.extend(directory.glob(f"*{ext}"))
        image_files.extend(directory.glob(f"*{ext.upper()}"))
    
    return sorted(image_files)


def copy_images(src_dir: Union[str, Path], dst_dir: Union[str, Path], 
                image_names: List[str]) -> None:
    """Copy images from source to destination directory"""
    src_dir = Path(src_dir)
    dst_dir = Path(dst_dir)
    ensure_dir(dst_dir)
    
    for image_name in image_names:
        src_path = src_dir / image_name
        dst_path = dst_dir / image_name
        if src_path.exists():
            shutil.copy2(src_path, dst_path)


def normalize_bbox(bbox: List[float], img_width: int, img_height: int) -> List[float]:
    """Normalize bounding box coordinates to [0, 1]"""
    x, y, w, h = bbox
    return [x / img_width, y / img_height, w / img_width, h / img_height]


def denormalize_bbox(bbox: List[float], img_width: int, img_height: int) -> List[float]:
    """Denormalize bounding box coordinates from [0, 1] to pixel values"""
    x, y, w, h = bbox
    return [x * img_width, y * img_height, w * img_width, h * img_height]


def xywh_to_xyxy(bbox: List[float]) -> List[float]:
    """Convert bbox from (x, y, width, height) to (x1, y1, x2, y2)"""
    x, y, w, h = bbox
    return [x, y, x + w, y + h]


def xyxy_to_xywh(bbox: List[float]) -> List[float]:
    """Convert bbox from (x1, y1, x2, y2) to (x, y, width, height)"""
    x1, y1, x2, y2 = bbox
    return [x1, y1, x2 - x1, y2 - y1]


def center_to_corner(bbox: List[float]) -> List[float]:
    """Convert bbox from (center_x, center_y, width, height) to (x, y, width, height)"""
    cx, cy, w, h = bbox
    return [cx - w/2, cy - h/2, w, h]


def corner_to_center(bbox: List[float]) -> List[float]:
    """Convert bbox from (x, y, width, height) to (center_x, center_y, width, height)"""
    x, y, w, h = bbox
    return [x + w/2, y + h/2, w, h]


def polygon_to_bbox(polygon: List[float]) -> List[float]:
    """Convert polygon to bounding box (x, y, width, height)"""
    x_coords = polygon[0::2]
    y_coords = polygon[1::2]
    
    x_min, x_max = min(x_coords), max(x_coords)
    y_min, y_max = min(y_coords), max(y_coords)
    
    return [x_min, y_min, x_max - x_min, y_max - y_min]


def bbox_to_polygon(bbox: List[float]) -> List[float]:
    """Convert bounding box to polygon (rectangle)"""
    x, y, w, h = bbox
    return [x, y, x + w, y, x + w, y + h, x, y + h]


def load_json(file_path: Union[str, Path]) -> Dict:
    """Load JSON file"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def save_json(data: Dict, file_path: Union[str, Path], indent: int = 2) -> None:
    """Save data to JSON file"""
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=indent, ensure_ascii=False)


def validate_bbox(bbox: List[float], img_width: int, img_height: int) -> bool:
    """Validate bounding box coordinates"""
    x, y, w, h = bbox
    return (0 <= x < img_width and 0 <= y < img_height and 
            w > 0 and h > 0 and x + w <= img_width and y + h <= img_height)


def clip_bbox(bbox: List[float], img_width: int, img_height: int) -> List[float]:
    """Clip bounding box to image boundaries"""
    x, y, w, h = bbox
    x = max(0, min(x, img_width - 1))
    y = max(0, min(y, img_height - 1))
    w = min(w, img_width - x)
    h = min(h, img_height - y)
    return [x, y, w, h]
