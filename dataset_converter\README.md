# 数据集转换器

一个全面的计算机视觉数据集格式转换工具包。支持多种任务，包括目标检测、实例分割、有向边界框(OBB)检测、关键点检测和图像分类。

## 支持的格式

### 输入/输出格式
- **COCO** (JSON格式) - Microsoft通用对象上下文数据集
- **YOLO** (TXT格式) - You Only Look Once
- **Pascal VOC** (XML格式) - 视觉对象类别数据集
- **LabelMe** (JSON格式) - 图像标注工具格式
- **ImageNet** (目录结构) - 图像分类数据集

### 支持的任务
- **目标检测** - 边界框检测
- **实例分割** - 像素级对象分割
- **OBB检测** - 有向边界框检测
- **关键点检测** - 人体姿态估计
- **图像分类** - 单/多类别分类

## 安装

1. 克隆或下载此仓库
2. 安装依赖：
```bash
pip install -r requirements.txt
```

## 快速开始

### 命令行使用

#### COCO转YOLO（目标检测）
```bash
python converters/coco_to_yolo.py \
    --coco_json path/to/annotations.json \
    --output_dir output/yolo_dataset \
    --task detection \
    --copy_images
```

#### YOLO转COCO（目标检测）
```bash
python converters/yolo_to_coco.py \
    --yolo_dir path/to/yolo/dataset \
    --images_dir path/to/images \
    --output_file output/coco_annotations.json \
    --task detection
```

#### Pascal VOC转YOLO（目标检测）
```bash
python converters/voc_to_yolo.py \
    --voc_dir path/to/voc/dataset \
    --output_dir output/yolo_dataset \
    --task detection \
    --copy_images
```

#### COCO转Pascal VOC（目标检测）
```bash
python converters/coco_to_voc.py \
    --coco_json path/to/annotations.json \
    --images_dir path/to/images \
    --output_dir output/voc_dataset \
    --task detection \
    --copy_images
```

#### LabelMe转COCO（混合任务）
```bash
python converters/labelme_to_coco.py \
    --labelme_dir path/to/labelme/dataset \
    --output_file output/coco_annotations.json \
    --task mixed \
    --extract_images
```

#### ImageNet转YOLO分类
```bash
python converters/imagenet_to_yolo_cls.py \
    --imagenet_dir path/to/imagenet/dataset \
    --output_dir output/yolo_classification \
    --copy_images \
    --train_ratio 0.8 \
    --val_ratio 0.1 \
    --test_ratio 0.1
```

### Python API使用

```python
from utils.coco_utils import COCODataset
from converters.coco_to_yolo import convert_coco_to_yolo_detection

# 加载COCO数据集
coco_dataset = COCODataset("path/to/annotations.json")

# 转换为YOLO格式
convert_coco_to_yolo_detection(
    coco_dataset,
    "output/yolo_dataset",
    copy_images=True
)
```

## 数据集格式详情

### COCO格式
- **结构**: JSON标注文件 + 图像目录
- **检测**: 边界框格式 [x, y, width, height]
- **分割**: 多边形坐标或RLE掩码
- **关键点**: 17点人体姿态关键点
- **类别**: 层次化类别结构

### YOLO格式
- **结构**: TXT标注文件 + 图像目录 + classes.txt
- **检测**: 归一化 [x_center, y_center, width, height]
- **分割**: 归一化多边形坐标
- **OBB**: 4点有向边界框坐标
- **姿态**: 边界框 + 归一化关键点坐标
- **分类**: 类别文件夹目录结构

### Pascal VOC格式
- **结构**: XML标注文件 + 图像目录 + ImageSets
- **检测**: 边界框格式 [xmin, ymin, xmax, ymax]
- **分割**: XML中的多边形坐标
- **属性**: 截断、困难标志

### LabelMe格式
- **结构**: JSON标注文件（可包含图像数据）
- **检测**: 矩形、圆形标注
- **分割**: 多边形标注
- **其他**: 点、线标注支持

### ImageNet格式
- **结构**: 类别文件夹目录结构
- **分类**: 按类别名称组织的图像目录
- **标注**: 可选的图像-类别映射文本文件

## 转换矩阵

| 从/到 | COCO | YOLO | Pascal VOC | LabelMe | ImageNet |
|-------|------|------|------------|---------|----------|
| **COCO** | ✓ | ✓ | ✓ | ✓ | - |
| **YOLO** | ✓ | ✓ | ✓ | ✓ | ✓* |
| **Pascal VOC** | ✓ | ✓ | ✓ | - | - |
| **LabelMe** | ✓ | ✓ | - | ✓ | - |
| **ImageNet** | - | ✓* | - | - | ✓ |

*仅分类任务

## 任务支持矩阵

| 格式 | 检测 | 分割 | OBB | 关键点 | 分类 |
|------|------|------|-----|--------|------|
| **COCO** | ✓ | ✓ | - | ✓ | - |
| **YOLO** | ✓ | ✓ | ✓ | ✓ | ✓ |
| **Pascal VOC** | ✓ | ✓ | - | - | - |
| **LabelMe** | ✓ | ✓ | - | ✓ | - |
| **ImageNet** | - | - | - | - | ✓ |

## 示例

查看 `examples/` 目录获取详细使用示例：
- `example_usage.py` - 所有转换器的综合示例

## 目录结构

```
dataset_converter/
├── utils/                  # 工具模块
│   ├── common.py          # 通用函数
│   ├── coco_utils.py      # COCO格式工具
│   ├── yolo_utils.py      # YOLO格式工具
│   ├── voc_utils.py       # Pascal VOC工具
│   └── labelme_utils.py   # LabelMe格式工具
├── converters/            # 转换脚本
│   ├── coco_to_yolo.py    # COCO → YOLO
│   ├── yolo_to_coco.py    # YOLO → COCO
│   ├── voc_to_coco.py     # VOC → COCO
│   ├── coco_to_voc.py     # COCO → VOC
│   ├── yolo_to_voc.py     # YOLO → VOC
│   ├── voc_to_yolo.py     # VOC → YOLO
│   ├── labelme_to_coco.py # LabelMe → COCO
│   ├── labelme_to_yolo.py # LabelMe → YOLO
│   ├── coco_to_labelme.py # COCO → LabelMe
│   ├── yolo_to_labelme.py # YOLO → LabelMe
│   ├── imagenet_to_yolo_cls.py  # ImageNet → YOLO分类
│   └── yolo_cls_to_imagenet.py  # YOLO分类 → ImageNet
├── examples/              # 使用示例
├── requirements.txt       # 依赖项
└── README.md             # 本文件
```

## 功能特性

- **全面的格式支持**: 支持所有主要CV数据集格式之间的转换
- **多种任务类型**: 支持检测、分割、OBB、关键点、分类任务
- **强大的错误处理**: 验证数据并处理边缘情况
- **灵活的选项**: 复制图像、分割数据集、合并分割
- **命令行界面**: 所有转换器的易用CLI
- **Python API**: 可导入并在自己的脚本中使用
- **详细文档**: 全面的示例和文档
- **LabelMe支持**: 完整支持LabelMe格式的导入导出

## 依赖要求

- Python 3.7+
- OpenCV (cv2)
- Pillow (PIL)
- NumPy
- pycocotools
- lxml
- tqdm
- matplotlib
- scikit-image
- PyYAML

## 贡献

1. Fork此仓库
2. 创建功能分支
3. 进行更改
4. 如适用，添加测试
5. 提交拉取请求

## 许可证

此项目为开源项目。请查看许可证文件了解详情。

## 故障排除

### 常见问题

1. **缺少依赖**: 使用 `pip install -r requirements.txt` 安装所有要求
2. **路径问题**: 使用绝对路径或确保相对路径正确
3. **图像格式问题**: 确保图像为支持的格式（JPG、PNG等）
4. **内存问题**: 对于大型数据集，分批处理或使用 `copy_images=False`

### 获取帮助

- 查看 `examples/` 目录中的示例
- 查看工具模块中的文档字符串
- 确保您的数据集遵循预期的格式结构

## 更新日志

### v1.1.0
- 添加LabelMe格式支持
- 完整的中文文档和注释
- 改进的错误处理和验证

### v1.0.0
- 初始发布
- 支持COCO、YOLO、Pascal VOC、ImageNet格式
- 目标检测、分割、OBB、关键点、分类任务
- 命令行界面和Python API
