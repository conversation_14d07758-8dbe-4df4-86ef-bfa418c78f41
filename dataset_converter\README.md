# Dataset Converter

A comprehensive toolkit for converting between different computer vision dataset formats. Supports multiple tasks including object detection, instance segmentation, oriented bounding box (OBB) detection, keypoint detection, and image classification.

## Supported Formats

### Input/Output Formats
- **COCO** (JSON format) - Microsoft Common Objects in Context
- **YOLO** (TXT format) - You Only Look Once
- **Pascal VOC** (XML format) - Visual Object Classes
- **ImageNet** (Directory structure) - Image classification

### Supported Tasks
- **Object Detection** - Bounding box detection
- **Instance Segmentation** - Pixel-level object segmentation
- **OBB Detection** - Oriented bounding box detection
- **Keypoint Detection** - Human pose estimation
- **Image Classification** - Single/multi-class classification

## Installation

1. Clone or download this repository
2. Install dependencies:
```bash
pip install -r requirements.txt
```

## Quick Start

### Command Line Usage

#### COCO to YOLO (Object Detection)
```bash
python converters/coco_to_yolo.py \
    --coco_json path/to/annotations.json \
    --output_dir output/yolo_dataset \
    --task detection \
    --copy_images
```

#### YOLO to COCO (Object Detection)
```bash
python converters/yolo_to_coco.py \
    --yolo_dir path/to/yolo/dataset \
    --images_dir path/to/images \
    --output_file output/coco_annotations.json \
    --task detection
```

#### Pascal VOC to YOLO (Object Detection)
```bash
python converters/voc_to_yolo.py \
    --voc_dir path/to/voc/dataset \
    --output_dir output/yolo_dataset \
    --task detection \
    --copy_images
```

#### COCO to Pascal VOC (Object Detection)
```bash
python converters/coco_to_voc.py \
    --coco_json path/to/annotations.json \
    --images_dir path/to/images \
    --output_dir output/voc_dataset \
    --task detection \
    --copy_images
```

#### ImageNet to YOLO Classification
```bash
python converters/imagenet_to_yolo_cls.py \
    --imagenet_dir path/to/imagenet/dataset \
    --output_dir output/yolo_classification \
    --copy_images \
    --train_ratio 0.8 \
    --val_ratio 0.1 \
    --test_ratio 0.1
```

### Python API Usage

```python
from utils.coco_utils import COCODataset
from converters.coco_to_yolo import convert_coco_to_yolo_detection

# Load COCO dataset
coco_dataset = COCODataset("path/to/annotations.json")

# Convert to YOLO format
convert_coco_to_yolo_detection(
    coco_dataset, 
    "output/yolo_dataset", 
    copy_images=True
)
```

## Dataset Format Details

### COCO Format
- **Structure**: JSON annotation file + images directory
- **Detection**: Bounding boxes in [x, y, width, height] format
- **Segmentation**: Polygon coordinates or RLE masks
- **Keypoints**: 17-point human pose keypoints
- **Categories**: Hierarchical category structure

### YOLO Format
- **Structure**: TXT annotation files + images directory + classes.txt
- **Detection**: Normalized [x_center, y_center, width, height]
- **Segmentation**: Normalized polygon coordinates
- **OBB**: 4-point oriented bounding box coordinates
- **Pose**: Bounding box + normalized keypoint coordinates
- **Classification**: Directory structure with class folders

### Pascal VOC Format
- **Structure**: XML annotation files + images directory + ImageSets
- **Detection**: Bounding boxes in [xmin, ymin, xmax, ymax] format
- **Segmentation**: Polygon coordinates in XML
- **Attributes**: Truncated, difficult flags

### ImageNet Format
- **Structure**: Directory structure with class folders
- **Classification**: Images organized in class-named directories
- **Annotation**: Optional text file with image-class mappings

## Conversion Matrix

| From/To | COCO | YOLO | Pascal VOC | ImageNet |
|---------|------|------|------------|----------|
| **COCO** | ✓ | ✓ | ✓ | - |
| **YOLO** | ✓ | ✓ | ✓ | ✓* |
| **Pascal VOC** | ✓ | ✓ | ✓ | - |
| **ImageNet** | - | ✓* | - | ✓ |

*Classification only

## Task Support Matrix

| Format | Detection | Segmentation | OBB | Keypoints | Classification |
|--------|-----------|--------------|-----|-----------|----------------|
| **COCO** | ✓ | ✓ | - | ✓ | - |
| **YOLO** | ✓ | ✓ | ✓ | ✓ | ✓ |
| **Pascal VOC** | ✓ | ✓ | - | - | - |
| **ImageNet** | - | - | - | - | ✓ |

## Examples

See the `examples/` directory for detailed usage examples:
- `example_usage.py` - Comprehensive examples for all converters

## Directory Structure

```
dataset_converter/
├── utils/                  # Utility modules
│   ├── common.py          # Common functions
│   ├── coco_utils.py      # COCO format utilities
│   ├── yolo_utils.py      # YOLO format utilities
│   └── voc_utils.py       # Pascal VOC utilities
├── converters/            # Conversion scripts
│   ├── coco_to_yolo.py    # COCO → YOLO
│   ├── yolo_to_coco.py    # YOLO → COCO
│   ├── voc_to_coco.py     # VOC → COCO
│   ├── coco_to_voc.py     # COCO → VOC
│   ├── yolo_to_voc.py     # YOLO → VOC
│   ├── voc_to_yolo.py     # VOC → YOLO
│   ├── imagenet_to_yolo_cls.py  # ImageNet → YOLO Classification
│   └── yolo_cls_to_imagenet.py  # YOLO Classification → ImageNet
├── examples/              # Usage examples
├── requirements.txt       # Dependencies
└── README.md             # This file
```

## Features

- **Comprehensive Format Support**: Convert between all major CV dataset formats
- **Multiple Task Types**: Support for detection, segmentation, OBB, keypoints, classification
- **Robust Error Handling**: Validates data and handles edge cases
- **Flexible Options**: Copy images, split datasets, merge splits
- **Command Line Interface**: Easy-to-use CLI for all converters
- **Python API**: Import and use in your own scripts
- **Detailed Documentation**: Comprehensive examples and documentation

## Requirements

- Python 3.7+
- OpenCV (cv2)
- Pillow (PIL)
- NumPy
- pycocotools
- lxml
- tqdm
- matplotlib
- scikit-image
- PyYAML

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is open source. Please check the license file for details.

## Troubleshooting

### Common Issues

1. **Missing dependencies**: Install all requirements with `pip install -r requirements.txt`
2. **Path issues**: Use absolute paths or ensure relative paths are correct
3. **Image format issues**: Ensure images are in supported formats (JPG, PNG, etc.)
4. **Memory issues**: For large datasets, process in batches or use `copy_images=False`

### Getting Help

- Check the examples in `examples/` directory
- Review the docstrings in utility modules
- Ensure your dataset follows the expected format structure

## Changelog

### v1.0.0
- Initial release
- Support for COCO, YOLO, Pascal VOC, ImageNet formats
- Object detection, segmentation, OBB, keypoints, classification tasks
- Command line interface and Python API
