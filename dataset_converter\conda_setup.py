#!/usr/bin/env python3
"""
Conda环境设置和依赖检查脚本
"""
import subprocess
import sys
import os
from pathlib import Path


def run_command(command, check=True):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, check=check)
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        return False, e.stdout, e.stderr


def check_conda():
    """检查conda是否可用"""
    success, stdout, stderr = run_command("conda --version", check=False)
    if success:
        print(f"✓ Conda已安装: {stdout.strip()}")
        return True
    else:
        print("✗ 未找到conda命令，请确保已安装Anaconda或Miniconda")
        return False


def check_environment():
    """检查当前是否在conda环境中"""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env:
        print(f"✓ 当前conda环境: {conda_env}")
        return True
    else:
        print("✗ 未激活conda环境")
        return False


def install_dependencies():
    """安装依赖包"""
    print("\n开始安装依赖包...")
    
    # conda安装的包
    conda_packages = [
        "numpy>=1.19.0",
        "pillow>=8.0.0", 
        "opencv>=4.5.0",
        "matplotlib>=3.3.0",
        "scikit-image>=0.18.0",
        "lxml>=4.6.0",
        "tqdm>=4.60.0",
        "pyyaml>=5.4.0"
    ]
    
    print("使用conda安装核心依赖...")
    conda_cmd = f"conda install -c conda-forge {' '.join(conda_packages)} -y"
    success, stdout, stderr = run_command(conda_cmd)
    
    if success:
        print("✓ conda依赖安装成功")
    else:
        print(f"✗ conda依赖安装失败: {stderr}")
        return False
    
    # pip安装的包
    pip_packages = ["pycocotools>=2.0.2"]
    
    print("使用pip安装特殊依赖...")
    for package in pip_packages:
        pip_cmd = f"pip install {package}"
        success, stdout, stderr = run_command(pip_cmd)
        
        if success:
            print(f"✓ {package} 安装成功")
        else:
            print(f"✗ {package} 安装失败: {stderr}")
            return False
    
    return True


def test_imports():
    """测试导入"""
    print("\n测试模块导入...")
    
    test_modules = [
        ("numpy", "np"),
        ("PIL", "PIL"),
        ("cv2", "cv2"),
        ("matplotlib", "plt"),
        ("skimage", "skimage"),
        ("lxml", "lxml"),
        ("tqdm", "tqdm"),
        ("yaml", "yaml"),
        ("pycocotools", "pycocotools")
    ]
    
    failed_imports = []
    
    for module_name, import_name in test_modules:
        try:
            if import_name == "np":
                import numpy as np
            elif import_name == "PIL":
                from PIL import Image
            elif import_name == "cv2":
                import cv2
            elif import_name == "plt":
                import matplotlib.pyplot as plt
            elif import_name == "skimage":
                import skimage
            elif import_name == "lxml":
                import lxml
            elif import_name == "tqdm":
                from tqdm import tqdm
            elif import_name == "yaml":
                import yaml
            elif import_name == "pycocotools":
                from pycocotools import mask
            
            print(f"✓ {module_name}")
        except ImportError as e:
            print(f"✗ {module_name}: {e}")
            failed_imports.append(module_name)
    
    if failed_imports:
        print(f"\n导入失败的模块: {', '.join(failed_imports)}")
        return False
    else:
        print("\n✓ 所有模块导入成功！")
        return True


def create_environment():
    """创建conda环境"""
    env_file = Path("environment.yml")
    if env_file.exists():
        print("使用environment.yml创建环境...")
        success, stdout, stderr = run_command("conda env create -f environment.yml")
        if success:
            print("✓ 环境创建成功")
            print("请运行以下命令激活环境:")
            print("conda activate dataset-converter")
            return True
        else:
            print(f"✗ 环境创建失败: {stderr}")
    
    return False


def main():
    """主函数"""
    print("数据集转换器 - Conda环境设置")
    print("=" * 50)
    
    # 检查conda
    if not check_conda():
        print("\n请先安装Anaconda或Miniconda:")
        print("https://docs.conda.io/en/latest/miniconda.html")
        return
    
    # 检查环境
    if not check_environment():
        print("\n选项:")
        print("1. 创建新的conda环境")
        print("2. 在当前环境中安装依赖")
        
        choice = input("请选择 (1/2): ").strip()
        
        if choice == "1":
            if create_environment():
                return
            else:
                print("环境创建失败，请手动创建环境")
                return
        elif choice == "2":
            print("将在当前环境中安装依赖...")
        else:
            print("无效选择")
            return
    
    # 安装依赖
    if install_dependencies():
        print("\n依赖安装完成！")
    else:
        print("\n依赖安装失败，请检查错误信息")
        return
    
    # 测试导入
    if test_imports():
        print("\n🎉 环境设置完成！可以开始使用数据集转换器了。")
        print("\n快速开始:")
        print("python convert_dataset.py")
    else:
        print("\n❌ 部分模块导入失败，请检查安装")


if __name__ == "__main__":
    main()
