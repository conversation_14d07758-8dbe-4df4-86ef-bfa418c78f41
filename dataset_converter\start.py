#!/usr/bin/env python3
"""
数据集转换器启动脚本
自动检查环境并启动主程序
"""
import sys
import os
import subprocess
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("❌ 错误：需要Python 3.7或更高版本")
        print(f"当前版本：{sys.version}")
        return False
    print(f"✓ Python版本：{sys.version.split()[0]}")
    return True


def check_conda_env():
    """检查conda环境"""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env:
        print(f"✓ Conda环境：{conda_env}")
        return True
    else:
        print("⚠️  未检测到conda环境")
        return False


def check_dependencies():
    """快速检查关键依赖"""
    required_modules = ['numpy', 'PIL', 'cv2', 'yaml']
    missing_modules = []
    
    for module in required_modules:
        try:
            if module == 'PIL':
                from PIL import Image
            elif module == 'cv2':
                import cv2
            elif module == 'yaml':
                import yaml
            else:
                __import__(module)
            print(f"✓ {module}")
        except ImportError:
            print(f"✗ {module}")
            missing_modules.append(module)
    
    return len(missing_modules) == 0, missing_modules


def install_dependencies():
    """安装缺失的依赖"""
    print("\n是否要自动安装依赖？")
    print("1. 是（推荐）")
    print("2. 否，手动安装")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == "1":
        print("正在运行自动安装脚本...")
        try:
            subprocess.run([sys.executable, "conda_setup.py"], check=True)
            return True
        except subprocess.CalledProcessError:
            print("自动安装失败，请手动安装依赖")
            return False
    else:
        print("\n手动安装说明：")
        print("conda install -c conda-forge numpy pillow opencv matplotlib scikit-image lxml tqdm pyyaml")
        print("pip install pycocotools")
        print("\n或运行：python conda_setup.py")
        return False


def main():
    """主函数"""
    print("🚀 数据集转换器启动检查")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查conda环境
    check_conda_env()
    
    # 检查依赖
    print("\n检查依赖...")
    deps_ok, missing = check_dependencies()
    
    if not deps_ok:
        print(f"\n❌ 缺少依赖：{', '.join(missing)}")
        if install_dependencies():
            print("依赖安装完成，重新检查...")
            deps_ok, missing = check_dependencies()
        else:
            print("请安装依赖后重新运行")
            sys.exit(1)
    
    if deps_ok:
        print("\n✅ 环境检查通过！")
        print("\n启动数据集转换器...")
        
        try:
            # 启动主程序
            from convert_dataset import main as converter_main
            converter_main()
        except ImportError as e:
            print(f"❌ 导入错误：{e}")
            print("请运行完整测试：python test_imports.py")
        except KeyboardInterrupt:
            print("\n👋 用户取消，再见！")
        except Exception as e:
            print(f"❌ 运行错误：{e}")
    else:
        print("❌ 环境检查失败，请解决依赖问题")
        sys.exit(1)


if __name__ == "__main__":
    main()
