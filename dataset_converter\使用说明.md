# 数据集转换器使用说明

## 概述

这是一个全面的计算机视觉数据集格式转换工具包，支持多种主流数据集格式之间的相互转换。

## 支持的格式

- **COCO** (JSON格式) - Microsoft通用对象上下文数据集
- **YOLO** (TXT格式) - You Only Look Once格式
- **Pascal VOC** (XML格式) - 视觉对象类别数据集
- **LabelMe** (JSON格式) - 图像标注工具格式
- **ImageNet** (目录结构) - 图像分类数据集

## 支持的任务

- 目标检测（边界框）
- 实例分割（多边形）
- 有向边界框检测（OBB）
- 关键点检测（人体姿态）
- 图像分类

## 安装步骤

1. 确保已安装Python 3.7或更高版本
2. 安装依赖包：
```bash
pip install -r requirements.txt
```

## 快速开始

### 方法一：使用主转换脚本（推荐）

运行主转换脚本，通过交互式菜单选择转换类型：

```bash
python convert_dataset.py
```

### 方法二：直接使用转换脚本

#### 1. COCO转YOLO

```bash
# 目标检测
python converters/coco_to_yolo.py \
    --coco_json data/coco/annotations.json \
    --output_dir output/yolo_dataset \
    --task detection \
    --copy_images

# 实例分割
python converters/coco_to_yolo.py \
    --coco_json data/coco/annotations.json \
    --output_dir output/yolo_segmentation \
    --task segmentation \
    --copy_images
```

#### 2. YOLO转COCO

```bash
# 目标检测
python converters/yolo_to_coco.py \
    --yolo_dir data/yolo_dataset \
    --images_dir data/images \
    --output_file output/coco_annotations.json \
    --task detection

# 实例分割
python converters/yolo_to_coco.py \
    --yolo_dir data/yolo_dataset \
    --images_dir data/images \
    --output_file output/coco_segmentation.json \
    --task segmentation
```

#### 3. LabelMe转COCO

```bash
# 混合任务（检测+分割）
python converters/labelme_to_coco.py \
    --labelme_dir data/labelme_dataset \
    --output_file output/coco_from_labelme.json \
    --task mixed \
    --extract_images \
    --images_output_dir output/images

# 仅检测
python converters/labelme_to_coco.py \
    --labelme_dir data/labelme_dataset \
    --output_file output/coco_detection.json \
    --task detection
```

#### 4. LabelMe转YOLO

```bash
# 检测任务
python converters/labelme_to_yolo.py \
    --labelme_dir data/labelme_dataset \
    --output_dir output/yolo_from_labelme \
    --task detection \
    --extract_images

# 分割任务
python converters/labelme_to_yolo.py \
    --labelme_dir data/labelme_dataset \
    --output_dir output/yolo_segmentation \
    --task segmentation \
    --extract_images
```

#### 5. COCO转LabelMe

```bash
python converters/coco_to_labelme.py \
    --coco_json data/coco/annotations.json \
    --images_dir data/coco/images \
    --output_dir output/labelme_dataset \
    --task mixed \
    --include_image_data
```

#### 6. Pascal VOC转换

```bash
# VOC转COCO
python converters/voc_to_coco.py \
    --voc_dir data/voc_dataset \
    --output_file output/coco_from_voc.json \
    --task detection

# VOC转YOLO
python converters/voc_to_yolo.py \
    --voc_dir data/voc_dataset \
    --output_dir output/yolo_from_voc \
    --task detection \
    --copy_images
```

#### 7. 图像分类转换

```bash
# ImageNet转YOLO分类
python converters/imagenet_to_yolo_cls.py \
    --imagenet_dir data/imagenet \
    --output_dir output/yolo_classification \
    --copy_images \
    --train_ratio 0.8 \
    --val_ratio 0.1 \
    --test_ratio 0.1

# YOLO分类转ImageNet
python converters/yolo_cls_to_imagenet.py \
    --yolo_dir data/yolo_classification \
    --output_dir output/imagenet_format \
    --copy_images \
    --merge_splits
```

## 常用参数说明

- `--copy_images`: 复制图像文件到输出目录
- `--extract_images`: 从LabelMe JSON文件中提取图像数据
- `--include_image_data`: 在JSON文件中包含base64编码的图像数据
- `--task`: 指定任务类型（detection, segmentation, mixed, pose, obb）
- `--train_ratio`, `--val_ratio`, `--test_ratio`: 数据集分割比例

## 数据集格式说明

### COCO格式
- 单个JSON文件包含所有标注信息
- 支持检测、分割、关键点任务
- 边界框格式：[x, y, width, height]

### YOLO格式
- 每张图像对应一个TXT标注文件
- classes.txt文件包含类别名称
- 坐标归一化到[0,1]范围
- 边界框格式：[x_center, y_center, width, height]

### LabelMe格式
- 每张图像对应一个JSON标注文件
- 可包含图像的base64编码数据
- 支持多种标注类型：矩形、多边形、点、线

### Pascal VOC格式
- 每张图像对应一个XML标注文件
- ImageSets目录包含数据集分割信息
- 边界框格式：[xmin, ymin, xmax, ymax]

## 注意事项

1. **路径问题**：建议使用绝对路径，避免相对路径错误
2. **图像格式**：支持常见图像格式（JPG、PNG、BMP等）
3. **内存使用**：大型数据集建议设置`copy_images=False`以节省内存
4. **数据验证**：转换前请确保数据集格式正确

## 故障排除

### 常见错误

1. **导入错误**：运行`python test_imports.py`检查依赖是否正确安装
2. **路径错误**：检查输入路径是否存在且格式正确
3. **格式错误**：确保数据集符合对应格式的标准结构

### 获取帮助

- 查看`examples/example_usage.py`了解详细使用示例
- 运行`python convert_dataset.py --help-all`查看所有转换器的帮助信息
- 检查各转换脚本的文档字符串

## 示例数据集结构

### COCO数据集结构
```
coco_dataset/
├── annotations/
│   ├── instances_train2017.json
│   └── instances_val2017.json
└── images/
    ├── train2017/
    └── val2017/
```

### YOLO数据集结构
```
yolo_dataset/
├── images/
│   ├── image1.jpg
│   └── image2.jpg
├── labels/
│   ├── image1.txt
│   └── image2.txt
├── classes.txt
└── data.yaml
```

### LabelMe数据集结构
```
labelme_dataset/
├── image1.jpg
├── image1.json
├── image2.jpg
└── image2.json
```

### Pascal VOC数据集结构
```
voc_dataset/
├── Annotations/
│   ├── image1.xml
│   └── image2.xml
├── JPEGImages/
│   ├── image1.jpg
│   └── image2.jpg
└── ImageSets/
    └── Main/
        ├── train.txt
        └── val.txt
```

## 联系支持

如果遇到问题或需要新功能，请提交issue或联系开发团队。
